/**
 * Test file to demonstrate API logging functionality
 * 
 * This file shows how the comprehensive API logging works with
 * both CC and AP API calls, including all the required logging features.
 */

import { 
	maskApiToken, 
	generateCorrelationId, 
	determineApiType, 
	extractLocationId,
	extractMaskedAuthToken,
	createApiRequestContext 
} from "./apiLogger";
import type { RequestOptions } from "@api/request";

/**
 * Test the API logging utilities
 */
export function testApiLogging(): void {
	console.log("🧪 Testing API Logging Utilities\n");

	// Test 1: Token masking
	console.log("1. Testing API Token Masking:");
	const testTokens = [
		"pit-19f7d314-acb6-45d7-bae7-2a56ccf789b6", // Real AP token format
		"cc_api_key_1234567890abcdefghijklmnopqrstuvwxyz", // Real CC token format
		"short", // Short token
		"", // Empty token
	];

	testTokens.forEach(token => {
		const masked = maskApiToken(token);
		console.log(`  Original: ${token.substring(0, 20)}${token.length > 20 ? '...' : ''}`);
		console.log(`  Masked:   ${masked}\n`);
	});

	// Test 2: Correlation ID generation
	console.log("2. Testing Correlation ID Generation:");
	for (let i = 0; i < 3; i++) {
		const correlationId = generateCorrelationId();
		console.log(`  Generated: ${correlationId}`);
	}
	console.log();

	// Test 3: API type determination
	console.log("3. Testing API Type Detection:");
	const testUrls = [
		"https://ccdemo.clinicore.eu/api/v1",
		"https://services.leadconnectorhq.com",
		"https://unknown-api.example.com",
		"",
	];

	testUrls.forEach(url => {
		const apiType = determineApiType(url);
		console.log(`  URL: ${url || '(empty)'}`);
		console.log(`  Type: ${apiType}\n`);
	});

	// Test 4: Location ID extraction
	console.log("4. Testing Location ID Extraction:");
	const testRequests: RequestOptions[] = [
		{
			url: "/contacts",
			method: "POST",
			data: { firstName: "John", locationId: "CIY0QcIvP7m9TxVWlvy3" }
		},
		{
			url: "/contacts",
			method: "GET",
			params: { locationId: "CIY0QcIvP7m9TxVWlvy3", limit: "10" }
		},
		{
			url: "/patients",
			method: "GET",
			data: { name: "Jane" }
		}
	];

	testRequests.forEach((request, index) => {
		const locationId = extractLocationId(request);
		console.log(`  Request ${index + 1}: ${request.method} ${request.url}`);
		console.log(`  Location ID: ${locationId || 'Not found'}\n`);
	});

	// Test 5: Auth token extraction and masking
	console.log("5. Testing Auth Token Extraction:");
	const testHeaders = [
		{ Authorization: "Bearer pit-19f7d314-acb6-45d7-bae7-2a56ccf789b6" },
		{ authorization: "Bearer cc_api_key_1234567890abcdefghijklmnopqrstuvwxyz" },
		{ Authorization: "Basic dXNlcjpwYXNz" },
		{ "Content-Type": "application/json" },
	];

	testHeaders.forEach((headers, index) => {
		const maskedToken = extractMaskedAuthToken(headers);
		console.log(`  Headers ${index + 1}: ${JSON.stringify(headers)}`);
		console.log(`  Masked Token: ${maskedToken || 'Not found'}\n`);
	});

	// Test 6: Complete API request context creation
	console.log("6. Testing Complete API Request Context:");
	
	// CC API example
	const ccRequest: RequestOptions = {
		url: "/patients/123",
		method: "GET",
	};
	const ccHeaders = {
		"Authorization": "Bearer cc_api_key_1234567890abcdefghijklmnopqrstuvwxyz",
		"Content-Type": "application/json",
	};
	const ccContext = createApiRequestContext(
		"https://ccdemo.clinicore.eu/api/v1",
		ccRequest,
		ccHeaders
	);

	console.log("  CC API Context:");
	console.log(`    Correlation ID: ${ccContext.correlationId}`);
	console.log(`    API Type: ${ccContext.apiType}`);
	console.log(`    Full URL: ${ccContext.fullUrl}`);
	console.log(`    Method: ${ccContext.method}`);
	console.log(`    Location ID: ${ccContext.locationId || 'N/A'}`);
	console.log(`    Masked Auth: ${ccContext.maskedAuthToken}\n`);

	// AP API example
	const apRequest: RequestOptions = {
		url: "/contacts",
		method: "POST",
		data: { 
			firstName: "John", 
			lastName: "Doe", 
			locationId: "CIY0QcIvP7m9TxVWlvy3" 
		},
		params: { limit: "10" }
	};
	const apHeaders = {
		"Authorization": "Bearer pit-19f7d314-acb6-45d7-bae7-2a56ccf789b6",
		"Content-Type": "application/json",
		"Version": "2021-04-15",
	};
	const apContext = createApiRequestContext(
		"https://services.leadconnectorhq.com",
		apRequest,
		apHeaders
	);

	console.log("  AP API Context:");
	console.log(`    Correlation ID: ${apContext.correlationId}`);
	console.log(`    API Type: ${apContext.apiType}`);
	console.log(`    Full URL: ${apContext.fullUrl}`);
	console.log(`    Method: ${apContext.method}`);
	console.log(`    Location ID: ${apContext.locationId}`);
	console.log(`    Masked Auth: ${apContext.maskedAuthToken}\n`);

	console.log("✅ API Logging Tests Completed!");
}

/**
 * Example of how the logging will appear in actual API calls
 */
export function demonstrateApiLogging(): void {
	console.log("\n🎭 Demonstrating API Logging Output\n");
	console.log("When making actual API calls, you'll see logs like:\n");

	console.log("📋 CC API Patient Retrieval:");
	console.log("2024-01-15T10:30:45.123Z [INFO] 🚀 CC_API Request: GET https://ccdemo.clinicore.eu/api/v1/patients/123");
	console.log("  correlationId: req_1705312245123_abc123def");
	console.log("  apiType: CC_API");
	console.log("  method: GET");
	console.log("  url: https://ccdemo.clinicore.eu/api/v1/patients/123");
	console.log("  authToken: cc_api_key***uvwxyz");
	console.log("2024-01-15T10:30:45.456Z [INFO] ✅ CC_API Response: 200 (333ms)");
	console.log("  correlationId: req_1705312245123_abc123def");
	console.log("  status: 200");
	console.log("  duration: 333\n");

	console.log("📋 AP API Contact Creation:");
	console.log("2024-01-15T10:31:15.789Z [INFO] 🚀 AP_API Request: POST https://services.leadconnectorhq.com/contacts");
	console.log("  correlationId: req_1705312275789_def456ghi");
	console.log("  apiType: AP_API");
	console.log("  method: POST");
	console.log("  url: https://services.leadconnectorhq.com/contacts");
	console.log("  locationId: CIY0QcIvP7m9TxVWlvy3");
	console.log("  authToken: pit-19f7d3***f789b6");
	console.log("2024-01-15T10:31:15.800Z [DEBUG] 📤 AP_API Request Payload");
	console.log("  correlationId: req_1705312275789_def456ghi");
	console.log("  method: POST");
	console.log("  payload: { firstName: 'John', lastName: 'Doe', locationId: 'CIY0QcIvP7m9TxVWlvy3' }");
	console.log("2024-01-15T10:31:16.234Z [INFO] ✅ AP_API Response: 201 (445ms)");
	console.log("  correlationId: req_1705312275789_def456ghi");
	console.log("  status: 201");
	console.log("  duration: 445\n");

	console.log("📋 API Error with Retry:");
	console.log("2024-01-15T10:32:00.111Z [INFO] 🚀 AP_API Request: GET https://services.leadconnectorhq.com/contacts/invalid");
	console.log("2024-01-15T10:32:00.555Z [ERROR] 💥 AP_API Error: HTTP 404: Not Found (attempt 1/3, retrying...)");
	console.log("  correlationId: req_1705312320111_ghi789jkl");
	console.log("  error: HTTP 404: Not Found");
	console.log("  attempt: 1");
	console.log("  maxAttempts: 3");
	console.log("  isRetrying: true");
	console.log("2024-01-15T10:32:01.666Z [ERROR] 💥 AP_API Error: HTTP 404: Not Found (final attempt 3/3)");
	console.log("  correlationId: req_1705312320111_ghi789jkl");
	console.log("  isRetrying: false\n");

	console.log("🎯 Key Benefits:");
	console.log("  ✅ Location ID tracking for AP requests");
	console.log("  ✅ Secure token masking (first 10 + last 10 chars)");
	console.log("  ✅ Complete URL logging with query parameters");
	console.log("  ✅ Request payload logging for debugging");
	console.log("  ✅ Correlation IDs for request/response matching");
	console.log("  ✅ Appropriate log levels (info/debug)");
	console.log("  ✅ Cloudflare Workers compatible");
	console.log("  ✅ Enhanced error context for debugging");
}

// Run tests if this file is executed directly
if (typeof window === "undefined" && typeof process !== "undefined") {
	testApiLogging();
	demonstrateApiLogging();
}
